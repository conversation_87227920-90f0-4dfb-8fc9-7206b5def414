# 寻迹功能移植完成报告

## 📋 移植概述

成功将STM32F103项目中的寻迹功能移植到TI MSPM0G3507项目中。移植保持了STM32项目的成功寻迹算法，同时充分利用了TI项目的任务调度系统优势。

## 🔧 移植内容

### 新增文件
1. **BSP/Inc/LineTracking.h** - 寻迹算法头文件
   - 定义寻迹算法接口函数
   - 配置寻迹参数（速度、转向系数等）
   - 适配TI项目的数据类型

2. **BSP/Src/LineTracking.c** - 寻迹算法实现文件
   - 移植STM32的寻迹算法核心逻辑
   - 实现差速转向适配函数
   - 集成TI项目的电机控制接口

### 修改文件
1. **APP/Inc/Task_App.h** - 添加寻迹任务声明
2. **APP/Src/Task_App.c** - 集成寻迹任务到任务调度系统

## ⚙️ 寻迹参数配置

```c
#define BASE_SPEED 30        // 基础速度百分比
#define KP_FACTOR 3          // 比例控制系数，调节转向强度
#define MAX_TURN_RATE 20     // 最大转向率，限制转向幅度
#define LOST_LINE_TIMEOUT 40 // 丢线保持时间(主循环次数)
```

## 🎯 任务调度配置

- **寻迹任务频率**: 50ms (20Hz)
- **任务优先级**: 3 (中等优先级)
- **传感器任务频率**: 10ms (100Hz)
- **OLED显示频率**: 50ms (20Hz)

## 🔄 工作流程

1. **传感器任务** (10ms) - 采集8路灰度传感器数据
2. **寻迹任务** (50ms) - 处理传感器数据，计算转向控制
3. **OLED任务** (50ms) - 显示系统状态和传感器数据

## 📊 核心算法

### 误差计算
- 基于8路传感器的加权位置算法
- 传感器权重：-7, -5, -3, -1, 1, 3, 5, 7
- 支持丢线记忆功能

### 差速控制
```c
left_speed = base_speed - turn_rate
right_speed = base_speed + turn_rate
```

### 比例控制
```c
turn_rate = error * KP_FACTOR
```

## 🚀 使用方法

### 编译和运行
1. 使用TI Code Composer Studio打开项目
2. 编译项目（应无编译错误）
3. 下载到MSPM0G3507开发板
4. 系统将自动启动寻迹功能

### OLED显示内容
- 第1行：左电机实际速度 (pps)
- 第2行：右电机实际速度 (pps)  
- 第3行：灰度传感器状态 (8位二进制)

### 参数调优建议

**如果寻迹效果不理想，可以调整以下参数：**

1. **转向过于敏感** - 减小 `KP_FACTOR` (默认3)
2. **转向不够灵敏** - 增大 `KP_FACTOR`
3. **转向幅度过大** - 减小 `MAX_TURN_RATE` (默认20)
4. **经常丢线** - 增大 `LOST_LINE_TIMEOUT` (默认40)
5. **速度过快/过慢** - 调整 `BASE_SPEED` (默认30)

## ✅ 移植优势

1. **算法保持** - 完全保留STM32项目的成功寻迹算法
2. **架构升级** - 利用TI项目更先进的任务调度系统
3. **代码规范** - 完全符合TI项目的命名和架构规范
4. **无缝集成** - 不影响现有功能，向后兼容
5. **性能优化** - 任务调度比主循环更高效

## 🔧 故障排除

### 常见问题
1. **机器人不动** - 检查电机连接和电源
2. **转向异常** - 调整KP_FACTOR参数
3. **传感器无响应** - 检查传感器连接和校准值
4. **OLED显示异常** - 检查I2C连接

### 调试建议
- 通过OLED显示监控传感器状态
- 观察电机速度变化是否合理
- 根据实际效果调整参数

## 📝 技术细节

### 数据类型适配
- `uint16_t` → `unsigned short`
- `uint8_t` → `unsigned char`
- `int16_t` → `short`

### 电机控制适配
- STM32: `differential_turn(baseSpeed, turnRate)`
- TI: `Motor_SetBothSpeed(left_speed, right_speed)`

### 传感器接口复用
- 直接使用TI现有的`No_MCU_Sensor`结构体
- 复用`Get_Digtal_For_User()`函数

---

**移植完成时间**: 2025年1月
**测试状态**: 代码编译通过，等待硬件测试
**维护建议**: 根据实际测试效果调优参数
