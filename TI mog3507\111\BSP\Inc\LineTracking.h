#ifndef __LINETRACKING_H__
#define __LINETRACKING_H__

#include "SysConfig.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"

#ifdef __cplusplus
extern "C" {
#endif

/*************************** 寻迹算法参数配置 ***************************/
#define BASE_SPEED 30        // 基础速度百分比
#define KP_FACTOR 3          // 比例控制系数，调节转向强度
#define MAX_TURN_RATE 20     // 最大转向率，限制转向幅度
#define LOST_LINE_TIMEOUT 40 // 丢线保持时间(主循环次数)

/*************************** 函数声明区域 *****************************/
/**
 * @brief 寻迹算法初始化，重置所有状态变量
 */
void LineTracking_Init(void);

/**
 * @brief 寻迹主处理函数，传感器数据处理和控制的入口
 * @param sensor 传感器结构体指针
 */
void LineTracking_Process(No_MCU_Sensor* sensor);

/**
 * @brief 计算寻迹误差，基于8路传感器的加权位置算法
 * @param sensor_digital 传感器数字状态（8位）
 * @return 计算得到的位置误差
 */
short LineTracking_CalculateError(unsigned char sensor_digital);

/**
 * @brief 寻迹控制算法，基于误差计算转向率并执行差速控制
 * @param error 位置误差值
 */
void LineTracking_Control(short error);

/**
 * @brief 设置丢线保持时间，用于调节寻迹参数
 * @param timeout 丢线保持时间
 */
void LineTracking_SetLostLineTimeout(unsigned short timeout);

/**
 * @brief 差速转向控制函数，适配TI项目的电机控制接口
 * @param base_speed 基础速度
 * @param turn_rate 转向率（正值右转，负值左转）
 */
void LineTracking_DifferentialTurn(short base_speed, short turn_rate);

#ifdef __cplusplus
}
#endif

#endif /* __LINETRACKING_H__ */
