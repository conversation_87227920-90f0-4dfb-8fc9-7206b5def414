#include "LineTracking.h"
#include "Motor.h"

/*************************** 寻迹状态变量 ***************************/
static short last_error = 0;                                    // 上次误差值，用于丢线记忆
static unsigned short lost_line_counter = 0;                    // 丢线计数器
static unsigned short lost_line_timeout = LOST_LINE_TIMEOUT;    // 可调节的丢线超时时间

/*************************** 函数实现区域 ***************************/

/**
 * @brief 寻迹算法初始化，重置所有状态变量
 */
void LineTracking_Init(void)
{
    last_error = 0;
    lost_line_counter = 0;
}

/**
 * @brief 计算寻迹误差，基于8路传感器的加权位置算法
 * @param sensor_digital 传感器数字状态（8位）
 * @return 计算得到的位置误差
 */
short LineTracking_CalculateError(unsigned char sensor_digital)
{
    short error = 0;
    short position = 0;
    unsigned char sensor_count = 0;

    // 计算加权位置，传感器权重：-7,-5,-3,-1,1,3,5,7
    for(int i = 0; i < 8; i++)
    {
        if(sensor_digital & (1 << i))  // 检测到黑线
        {
            position += (i * 2 - 7);   // 位置权重计算
            sensor_count++;
        }
    }

    if(sensor_count > 0)  // 检测到黑线
    {
        error = position / sensor_count;  // 平均位置误差
        last_error = error;               // 保存误差用于丢线记忆
        lost_line_counter = 0;            // 重置丢线计数
    }
    else  // 丢线状态
    {
        if(lost_line_counter < lost_line_timeout)
        {
            error = last_error;           // 保持上次转向方向
            lost_line_counter++;          // 丢线计数递增
        }
        else
        {
            error = 0;                    // 超时后恢复直走
        }
    }

    return error;
}

/**
 * @brief 差速转向控制函数，适配TI项目的电机控制接口
 * @param base_speed 基础速度
 * @param turn_rate 转向率（正值右转，负值左转）
 */
void LineTracking_DifferentialTurn(short base_speed, short turn_rate)
{
    float left_speed = base_speed - turn_rate;   // 左轮速度计算
    float right_speed = base_speed + turn_rate;  // 右轮速度计算
    
    // 限制速度范围在-100到100之间
    if(left_speed > 100.0f) left_speed = 100.0f;
    if(left_speed < -100.0f) left_speed = -100.0f;
    if(right_speed > 100.0f) right_speed = 100.0f;
    if(right_speed < -100.0f) right_speed = -100.0f;
    
    // 调用TI项目的电机控制接口
    Motor_SetBothSpeed(left_speed, right_speed);
}

/**
 * @brief 寻迹控制算法，基于误差计算转向率并执行差速控制
 * @param error 位置误差值
 */
void LineTracking_Control(short error)
{
    short turn_rate = error * KP_FACTOR;  // 比例控制计算转向率

    // 限制转向率在最大范围内
    if(turn_rate > MAX_TURN_RATE) turn_rate = MAX_TURN_RATE;
    if(turn_rate < -MAX_TURN_RATE) turn_rate = -MAX_TURN_RATE;

    // 执行差速转向控制
    LineTracking_DifferentialTurn(BASE_SPEED, turn_rate);
}

/**
 * @brief 寻迹主处理函数，传感器数据处理和控制的入口
 * @param sensor 传感器结构体指针
 */
void LineTracking_Process(No_MCU_Sensor* sensor)
{
    // 获取传感器数字状态
    unsigned char digital = Get_Digtal_For_User(sensor);
    
    // 计算寻迹误差
    short error = LineTracking_CalculateError(digital);
    
    // 执行寻迹控制
    LineTracking_Control(error);
}

/**
 * @brief 设置丢线保持时间，用于调节寻迹参数
 * @param timeout 丢线保持时间
 */
void LineTracking_SetLostLineTimeout(unsigned short timeout)
{
    lost_line_timeout = timeout;
}
