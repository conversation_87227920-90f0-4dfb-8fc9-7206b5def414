******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 01:29:28 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003fc1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000054a0  0001ab60  R  X
  SRAM                  20200000   00008000  00000414  00007bec  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004a48   00004a48    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004988   00004988    r-x .text
00004a50    00004a50    00000a58   00000a58    r--
  00004a50    00004a50    00000a00   00000a00    r-- .rodata
  00005450    00005450    00000058   00000058    r-- .cinit
20200000    20200000    00000217   00000000    rw-
  20200000    20200000    000001b5   00000000    rw- .bss
  202001b8    202001b8    0000005f   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004988     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    00000208     Task_App.o (.text.Task_OLED)
                  00000eb8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001094    000001d8     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000126c    000001b0     Task.o (.text.Task_Start)
                  0000141c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000015ae    00000002     Task_App.o (.text.Task_IdleFunction)
                  000015b0    0000014c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000016fc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001838    00000134            : qsort.c.obj (.text.qsort)
                  0000196c    00000130     OLED.o (.text.OLED_ShowChar)
                  00001a9c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001bbc    00000110     OLED.o (.text.OLED_Init)
                  00001ccc    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001dd8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001edc    000000f0     Motor.o (.text.Motor_SetDirc)
                  00001fcc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000020b0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000218c    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00002268    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002340    000000c4     LineTracking.o (.text.LineTracking_CalculateError)
                  00002404    000000b4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000024b8    000000b4     Task.o (.text.Task_Add)
                  0000256c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00002616    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002618    000000a8     Motor.o (.text.Motor_SetSpeed)
                  000026c0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002762    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002764    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002804    0000009c     LineTracking.o (.text.LineTracking_DifferentialTurn)
                  000028a0    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00002938    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  000029c4    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00002a48    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002acc    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002b4e    00000002     --HOLE-- [fill = 0]
                  00002b50    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002bcc    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002c40    00000070     Task_App.o (.text.Task_Init)
                  00002cb0    0000006e     OLED.o (.text.OLED_ShowString)
                  00002d1e    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002d8a    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002df4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002e5c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002ec2    00000002     --HOLE-- [fill = 0]
                  00002ec4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002f28    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002f8c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002fee    00000002     --HOLE-- [fill = 0]
                  00002ff0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003052    00000002     --HOLE-- [fill = 0]
                  00003054    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  000030b4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003112    00000002     --HOLE-- [fill = 0]
                  00003114    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003170    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000031cc    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003228    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00003280    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000032d8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003330    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003386    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000033d8    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00003428    00000050     LineTracking.o (.text.LineTracking_Control)
                  00003478    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000034c8    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00003514    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00003560    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000035ac    0000004c     OLED.o (.text.OLED_Printf)
                  000035f8    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00003644    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000368e    00000002     --HOLE-- [fill = 0]
                  00003690    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000036da    00000002     --HOLE-- [fill = 0]
                  000036dc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003724    00000048     ADC.o (.text.adc_getValue)
                  0000376c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000037b0    00000044     OLED.o (.text.mspm0_i2c_disable)
                  000037f4    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00003836    00000002     --HOLE-- [fill = 0]
                  00003838    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000387a    00000002     --HOLE-- [fill = 0]
                  0000387c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000038bc    00000040     Task_App.o (.text.Task_GraySensor)
                  000038fc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000393c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000397c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000039bc    0000003e     Task.o (.text.Task_CMP)
                  000039fa    00000002     --HOLE-- [fill = 0]
                  000039fc    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003a38    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003a74    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003ab0    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00003aec    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003b28    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003b64    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003ba0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003bdc    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003c16    00000002     --HOLE-- [fill = 0]
                  00003c18    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003c52    00000002     --HOLE-- [fill = 0]
                  00003c54    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003c88    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003cbc    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00003cec    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00003d1c    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003d4c    0000002c     Interrupt.o (.text.Interrupt_Init)
                  00003d78    0000002c     Motor.o (.text.Motor_Start)
                  00003da4    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003dd0    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003dfc    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003e28    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003e54    0000002a     LineTracking.o (.text.LineTracking_Process)
                  00003e7e    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003ea6    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003ece    00000002     --HOLE-- [fill = 0]
                  00003ed0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003ef8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003f20    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00003f48    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00003f70    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003f98    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00003fc0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003fe8    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000400e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004034    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004058    00000024     Motor.o (.text.Motor_SetBothSpeed)
                  0000407c    00000024     Task_App.o (.text.Task_LineTracking)
                  000040a0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000040c4    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000040e8    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  0000410a    00000002     --HOLE-- [fill = 0]
                  0000410c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000412c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  0000414c    00000020     SysTick.o (.text.Delay)
                  0000416c    00000020     main.o (.text.main)
                  0000418c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000041aa    00000002     --HOLE-- [fill = 0]
                  000041ac    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000041ca    00000002     --HOLE-- [fill = 0]
                  000041cc    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  000041e8    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00004204    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00004220    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  0000423c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004258    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00004274    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004290    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000042ac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000042c8    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000042e4    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00004300    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  0000431c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004338    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004354    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004370    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000438c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000043a8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000043c0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000043d8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000043f0    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004408    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004420    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004438    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00004450    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004468    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004480    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004498    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000044b0    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000044c8    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000044e0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000044f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004510    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00004528    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00004540    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004558    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00004570    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004588    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000045a0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000045b8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000045d0    00000018     OLED.o (.text.DL_I2C_reset)
                  000045e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004600    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004618    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004630    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004648    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004660    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004678    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004690    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000046a8    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000046c0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000046d8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000046f0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00004708    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00004720    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004738    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00004750    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004768    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000477e    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00004794    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000047aa    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000047c0    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000047d6    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000047ec    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004802    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00004816    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  0000482a    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000483e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004852    00000002     --HOLE-- [fill = 0]
                  00004854    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004868    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000487c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004890    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000048a4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000048b8    00000014     LineTracking.o (.text.LineTracking_Init)
                  000048cc    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000048e0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000048f4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004906    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004918    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000492a    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  0000493a    00000002     --HOLE-- [fill = 0]
                  0000493c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000494c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000495c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000496c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  0000497c    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000498a    00000002     --HOLE-- [fill = 0]
                  0000498c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000499a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000049a8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000049b6    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000049c2    00000002     --HOLE-- [fill = 0]
                  000049c4    0000000c     SysTick.o (.text.Sys_GetTick)
                  000049d0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000049da    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000049e4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000049f4    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000049fe    0000000a            : vsprintf.c.obj (.text._outc)
                  00004a08    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004a10    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004a18    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004a20    00000006     libc.a : exit.c.obj (.text:abort)
                  00004a26    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004a2a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004a2e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004a32    00000002     --HOLE-- [fill = 0]
                  00004a34    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004a44    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00005450    00000058     
                  00005450    00000030     (.cinit..data.load) [load image, compression = lzss]
                  00005480    0000000c     (__TI_handler_table)
                  0000548c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005494    00000010     (__TI_cinit_table)
                  000054a4    00000004     --HOLE-- [fill = 0]

.rodata    0    00004a50    00000a00     
                  00004a50    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00005040    0000021c     OLED_Font.o (.rodata.asc2_0806)
                  0000525c    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000525f    00000001     --HOLE-- [fill = 0]
                  00005260    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005361    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00005363    00000001     --HOLE-- [fill = 0]
                  00005364    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000538c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000053a4    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  000053bc    00000016     Task_App.o (.rodata.str1.11683036942922059812.1)
                  000053d2    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000053e3    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000053f4    0000000d     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00005401    0000000b     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000540c    0000000b     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00005417    0000000b     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00005422    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000542c    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005434    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  0000543c    00000005     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005441    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005443    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00005445    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001b5     UNINITIALIZED
                  20200000    000000f0     Task.o (.bss.Task_Schedule)
                  202000f0    000000b0     (.common:GraySensor)
                  202001a0    00000010     (.common:Gray_Anolog)
                  202001b0    00000004     (.common:ExISR_Flag)
                  202001b4    00000001     (.common:Gray_Digtal)

.data      0    202001b8    0000005f     UNINITIALIZED
                  202001b8    00000020     Motor.o (.data.Motor_Left)
                  202001d8    00000020     Motor.o (.data.Motor_Right)
                  202001f8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202001fc    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200200    00000004     SysTick.o (.data.delayTick)
                  20200204    00000004     Task_App.o (.data.last_time)
                  20200208    00000004     SysTick.o (.data.uwTick)
                  2020020c    00000002     Task_App.o (.data.last_encoder_left)
                  2020020e    00000002     Task_App.o (.data.last_encoder_right)
                  20200210    00000002     LineTracking.o (.data.last_error)
                  20200212    00000002     LineTracking.o (.data.lost_line_counter)
                  20200214    00000002     LineTracking.o (.data.lost_line_timeout)
                  20200216    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3418    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3458    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       734     73        205    
       Interrupt.o                      378     0         4      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1112    73        209    
                                                                 
    .\BSP\Src\
       OLED_Font.o                      0       2060      0      
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1014    0         0      
       Task.o                           674     0         241    
       Motor.o                          556     0         64     
       LineTracking.o                   494     0         6      
       ADC.o                            236     0         0      
       SysTick.o                        84      0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4912    2060      319    
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1116    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5736    291       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     418     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       udivmoddi4.S.obj                 162     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2444    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       84        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     18782   2823      1044   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005494 records: 2, size/record: 8, table size: 16
	.data: load addr=00005450, load size=00000030 bytes, run addr=202001b8, run size=0000005f bytes, compression=lzss
	.bss: load addr=0000548c, load size=00000008 bytes, run addr=20200000, run size=000001b5 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005480 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000141d     000049e4     000049e2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003fc1     00004a34     00004a2e   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004a27  ADC0_IRQHandler                      
00004a27  ADC1_IRQHandler                      
00004a27  AES_IRQHandler                       
00004a2a  C$$EXIT                              
00004a27  CANFD0_IRQHandler                    
00004a27  DAC0_IRQHandler                      
0000387d  DL_ADC12_setClockConfig              
000049d1  DL_Common_delayCycles                
00003515  DL_DMA_initChannel                   
000030b5  DL_I2C_fillControllerTXFIFO          
0000400f  DL_I2C_setClockConfig                
000020b1  DL_SYSCTL_configSYSPLL               
00002ec5  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000376d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001dd9  DL_Timer_initFourCCPWMMode           
00004355  DL_Timer_setCaptCompUpdateMethod     
00004691  DL_Timer_setCaptureCompareOutCtl     
0000494d  DL_Timer_setCaptureCompareValue      
00004371  DL_Timer_setClockConfig              
000036dd  DL_UART_init                         
000048f5  DL_UART_setClockConfig               
00004a27  DMA_IRQHandler                       
202001f8  Data_MotorEncoder                    
00004a27  Default_Handler                      
0000414d  Delay                                
202001b0  ExISR_Flag                           
00004a27  GROUP0_IRQHandler                    
00002405  GROUP1_IRQHandler                    
0000218d  Get_Analog_value                     
00003ab1  Get_Anolog_Value                     
0000497d  Get_Digtal_For_User                  
202000f0  GraySensor                           
202001a0  Gray_Anolog                          
202001b4  Gray_Digtal                          
00004a2b  HOSTexit                             
00004a27  HardFault_Handler                    
00004a27  I2C0_IRQHandler                      
00004a27  I2C1_IRQHandler                      
00002d8b  I2C_OLED_Clear                       
00003aed  I2C_OLED_Set_Pos                     
000028a1  I2C_OLED_WR_Byte                     
00003055  I2C_OLED_i2c_sda_unlock              
00003d4d  Interrupt_Init                       
00002341  LineTracking_CalculateError          
00003429  LineTracking_Control                 
00002805  LineTracking_DifferentialTurn        
000048b9  LineTracking_Init                    
00003e55  LineTracking_Process                 
202001b8  Motor_Left                           
202001d8  Motor_Right                          
00004059  Motor_SetBothSpeed                   
00002619  Motor_SetSpeed                       
00003d79  Motor_Start                          
00004a27  NMI_Handler                          
000015b1  No_MCU_Ganv_Sensor_Init_Frist        
000037f5  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001bbd  OLED_Init                            
000035ad  OLED_Printf                          
0000196d  OLED_ShowChar                        
00002cb1  OLED_ShowString                      
00004a27  PendSV_Handler                       
00004a27  RTC_IRQHandler                       
00004a2f  Reset_Handler                        
00004a27  SPI0_IRQHandler                      
00004a27  SPI1_IRQHandler                      
00004a27  SVC_Handler                          
000035f9  SYSCFG_DL_ADC1_init                  
00003ced  SYSCFG_DL_DMA_CH_RX_init             
00004739  SYSCFG_DL_DMA_CH_TX_init             
000049b7  SYSCFG_DL_DMA_init                   
00001095  SYSCFG_DL_GPIO_init                  
00003229  SYSCFG_DL_I2C_MPU6050_init           
00002f29  SYSCFG_DL_I2C_OLED_init              
00002939  SYSCFG_DL_Motor_PWM_init             
00003115  SYSCFG_DL_SYSCTL_init                
0000495d  SYSCFG_DL_SYSTICK_init               
000029c5  SYSCFG_DL_UART0_init                 
00003da5  SYSCFG_DL_init                       
00002765  SYSCFG_DL_initPower                  
00004a09  SysTick_Handler                      
00003f71  SysTick_Increasment                  
000049c5  Sys_GetTick                          
00004a27  TIMA0_IRQHandler                     
00004a27  TIMA1_IRQHandler                     
00004a27  TIMG0_IRQHandler                     
00004a27  TIMG12_IRQHandler                    
00004a27  TIMG6_IRQHandler                     
00004a27  TIMG7_IRQHandler                     
00004a27  TIMG8_IRQHandler                     
00004907  TI_memcpy_small                      
000049a9  TI_memset_small                      
000024b9  Task_Add                             
000038bd  Task_GraySensor                      
000015af  Task_IdleFunction                    
00002c41  Task_Init                            
0000407d  Task_LineTracking                    
00000cb1  Task_OLED                            
0000126d  Task_Start                           
00004a27  UART0_IRQHandler                     
00004a27  UART1_IRQHandler                     
00004a27  UART2_IRQHandler                     
00004a27  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005494  __TI_CINIT_Base                      
000054a4  __TI_CINIT_Limit                     
000054a4  __TI_CINIT_Warm                      
00005480  __TI_Handler_Table_Base              
0000548c  __TI_Handler_Table_Limit             
00003ba1  __TI_auto_init_nobinit_nopinit       
00002b51  __TI_decompress_lzss                 
00004919  __TI_decompress_none                 
00003281  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000047ed  __TI_zero_init_nomemset              
00001427  __adddf3                             
00005260  __aeabi_ctype_table_                 
00005260  __aeabi_ctype_table_C                
00003691  __aeabi_d2iz                         
00003839  __aeabi_d2uiz                        
00001427  __aeabi_dadd                         
00002f8d  __aeabi_dcmpeq                       
00002fc9  __aeabi_dcmpge                       
00002fdd  __aeabi_dcmpgt                       
00002fb5  __aeabi_dcmple                       
00002fa1  __aeabi_dcmplt                       
00001ccd  __aeabi_ddiv                         
00001fcd  __aeabi_dmul                         
0000141d  __aeabi_dsub                         
202001fc  __aeabi_errno                        
00004a11  __aeabi_errno_addr                   
0000393d  __aeabi_f2d                          
00002ff1  __aeabi_fcmpeq                       
0000302d  __aeabi_fcmpge                       
00003041  __aeabi_fcmpgt                       
00003019  __aeabi_fcmple                       
00003005  __aeabi_fcmplt                       
00002acd  __aeabi_fdiv                         
00003dfd  __aeabi_i2d                          
00003b29  __aeabi_i2f                          
00003331  __aeabi_idiv                         
00002617  __aeabi_idiv0                        
00003331  __aeabi_idivmod                      
00002763  __aeabi_ldiv0                        
000041ad  __aeabi_llsl                         
000040c5  __aeabi_lmul                         
00004a19  __aeabi_memcpy                       
00004a19  __aeabi_memcpy4                      
00004a19  __aeabi_memcpy8                      
0000498d  __aeabi_memset                       
0000498d  __aeabi_memset4                      
0000498d  __aeabi_memset8                      
000040a1  __aeabi_ui2d                         
00003f99  __aeabi_ui2f                         
000038fd  __aeabi_uidiv                        
000038fd  __aeabi_uidivmod                     
000048cd  __aeabi_uldivmod                     
000041ad  __ashldi3                            
ffffffff  __binit__                            
00002df5  __cmpdf2                             
00003bdd  __cmpsf2                             
00001ccd  __divdf3                             
00002acd  __divsf3                             
00002df5  __eqdf2                              
00003bdd  __eqsf2                              
0000393d  __extendsfdf2                        
00003691  __fixdfsi                            
00003839  __fixunsdfsi                         
00003dfd  __floatsidf                          
00003b29  __floatsisf                          
000040a1  __floatunsidf                        
00003f99  __floatunsisf                        
00002bcd  __gedf2                              
00003b65  __gesf2                              
00002bcd  __gtdf2                              
00003b65  __gtsf2                              
00002df5  __ledf2                              
00003bdd  __lesf2                              
00002df5  __ltdf2                              
00003bdd  __ltsf2                              
UNDEFED   __mpu_init                           
00001fcd  __muldf3                             
000040c5  __muldi3                             
00003c19  __muldsi3                            
00002df5  __nedf2                              
00003bdd  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000141d  __subdf3                             
000026c1  __udivmoddi4                         
00003fc1  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00004a45  _system_pre_init                     
00004a21  abort                                
00003725  adc_getValue                         
00005040  asc2_0806                            
00004a50  asc2_1608                            
0000397d  atoi                                 
ffffffff  binit                                
00002d1f  convertAnalogToDigital               
20200200  delayTick                            
00003171  frexp                                
00003171  frexpl                               
00000000  interruptVectors                     
00002269  ldexp                                
00002269  ldexpl                               
0000416d  main                                 
000040e9  memccpy                              
0000256d  normalizeAnalogValues                
00001839  qsort                                
00002269  scalbn                               
00002269  scalbnl                              
20200208  uwTick                               
00003e29  vsprintf                             
0000496d  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000cb1  Task_OLED                            
00001095  SYSCFG_DL_GPIO_init                  
0000126d  Task_Start                           
0000141d  __aeabi_dsub                         
0000141d  __subdf3                             
00001427  __adddf3                             
00001427  __aeabi_dadd                         
000015af  Task_IdleFunction                    
000015b1  No_MCU_Ganv_Sensor_Init_Frist        
00001839  qsort                                
0000196d  OLED_ShowChar                        
00001bbd  OLED_Init                            
00001ccd  __aeabi_ddiv                         
00001ccd  __divdf3                             
00001dd9  DL_Timer_initFourCCPWMMode           
00001fcd  __aeabi_dmul                         
00001fcd  __muldf3                             
000020b1  DL_SYSCTL_configSYSPLL               
0000218d  Get_Analog_value                     
00002269  ldexp                                
00002269  ldexpl                               
00002269  scalbn                               
00002269  scalbnl                              
00002341  LineTracking_CalculateError          
00002405  GROUP1_IRQHandler                    
000024b9  Task_Add                             
0000256d  normalizeAnalogValues                
00002617  __aeabi_idiv0                        
00002619  Motor_SetSpeed                       
000026c1  __udivmoddi4                         
00002763  __aeabi_ldiv0                        
00002765  SYSCFG_DL_initPower                  
00002805  LineTracking_DifferentialTurn        
000028a1  I2C_OLED_WR_Byte                     
00002939  SYSCFG_DL_Motor_PWM_init             
000029c5  SYSCFG_DL_UART0_init                 
00002acd  __aeabi_fdiv                         
00002acd  __divsf3                             
00002b51  __TI_decompress_lzss                 
00002bcd  __gedf2                              
00002bcd  __gtdf2                              
00002c41  Task_Init                            
00002cb1  OLED_ShowString                      
00002d1f  convertAnalogToDigital               
00002d8b  I2C_OLED_Clear                       
00002df5  __cmpdf2                             
00002df5  __eqdf2                              
00002df5  __ledf2                              
00002df5  __ltdf2                              
00002df5  __nedf2                              
00002ec5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002f29  SYSCFG_DL_I2C_OLED_init              
00002f8d  __aeabi_dcmpeq                       
00002fa1  __aeabi_dcmplt                       
00002fb5  __aeabi_dcmple                       
00002fc9  __aeabi_dcmpge                       
00002fdd  __aeabi_dcmpgt                       
00002ff1  __aeabi_fcmpeq                       
00003005  __aeabi_fcmplt                       
00003019  __aeabi_fcmple                       
0000302d  __aeabi_fcmpge                       
00003041  __aeabi_fcmpgt                       
00003055  I2C_OLED_i2c_sda_unlock              
000030b5  DL_I2C_fillControllerTXFIFO          
00003115  SYSCFG_DL_SYSCTL_init                
00003171  frexp                                
00003171  frexpl                               
00003229  SYSCFG_DL_I2C_MPU6050_init           
00003281  __TI_ltoa                            
00003331  __aeabi_idiv                         
00003331  __aeabi_idivmod                      
00003429  LineTracking_Control                 
00003515  DL_DMA_initChannel                   
000035ad  OLED_Printf                          
000035f9  SYSCFG_DL_ADC1_init                  
00003691  __aeabi_d2iz                         
00003691  __fixdfsi                            
000036dd  DL_UART_init                         
00003725  adc_getValue                         
0000376d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000037f5  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003839  __aeabi_d2uiz                        
00003839  __fixunsdfsi                         
0000387d  DL_ADC12_setClockConfig              
000038bd  Task_GraySensor                      
000038fd  __aeabi_uidiv                        
000038fd  __aeabi_uidivmod                     
0000393d  __aeabi_f2d                          
0000393d  __extendsfdf2                        
0000397d  atoi                                 
00003ab1  Get_Anolog_Value                     
00003aed  I2C_OLED_Set_Pos                     
00003b29  __aeabi_i2f                          
00003b29  __floatsisf                          
00003b65  __gesf2                              
00003b65  __gtsf2                              
00003ba1  __TI_auto_init_nobinit_nopinit       
00003bdd  __cmpsf2                             
00003bdd  __eqsf2                              
00003bdd  __lesf2                              
00003bdd  __ltsf2                              
00003bdd  __nesf2                              
00003c19  __muldsi3                            
00003ced  SYSCFG_DL_DMA_CH_RX_init             
00003d4d  Interrupt_Init                       
00003d79  Motor_Start                          
00003da5  SYSCFG_DL_init                       
00003dfd  __aeabi_i2d                          
00003dfd  __floatsidf                          
00003e29  vsprintf                             
00003e55  LineTracking_Process                 
00003f71  SysTick_Increasment                  
00003f99  __aeabi_ui2f                         
00003f99  __floatunsisf                        
00003fc1  _c_int00_noargs                      
0000400f  DL_I2C_setClockConfig                
00004059  Motor_SetBothSpeed                   
0000407d  Task_LineTracking                    
000040a1  __aeabi_ui2d                         
000040a1  __floatunsidf                        
000040c5  __aeabi_lmul                         
000040c5  __muldi3                             
000040e9  memccpy                              
0000414d  Delay                                
0000416d  main                                 
000041ad  __aeabi_llsl                         
000041ad  __ashldi3                            
00004355  DL_Timer_setCaptCompUpdateMethod     
00004371  DL_Timer_setClockConfig              
00004691  DL_Timer_setCaptureCompareOutCtl     
00004739  SYSCFG_DL_DMA_CH_TX_init             
000047ed  __TI_zero_init_nomemset              
000048b9  LineTracking_Init                    
000048cd  __aeabi_uldivmod                     
000048f5  DL_UART_setClockConfig               
00004907  TI_memcpy_small                      
00004919  __TI_decompress_none                 
0000494d  DL_Timer_setCaptureCompareValue      
0000495d  SYSCFG_DL_SYSTICK_init               
0000496d  wcslen                               
0000497d  Get_Digtal_For_User                  
0000498d  __aeabi_memset                       
0000498d  __aeabi_memset4                      
0000498d  __aeabi_memset8                      
000049a9  TI_memset_small                      
000049b7  SYSCFG_DL_DMA_init                   
000049c5  Sys_GetTick                          
000049d1  DL_Common_delayCycles                
00004a09  SysTick_Handler                      
00004a11  __aeabi_errno_addr                   
00004a19  __aeabi_memcpy                       
00004a19  __aeabi_memcpy4                      
00004a19  __aeabi_memcpy8                      
00004a21  abort                                
00004a27  ADC0_IRQHandler                      
00004a27  ADC1_IRQHandler                      
00004a27  AES_IRQHandler                       
00004a27  CANFD0_IRQHandler                    
00004a27  DAC0_IRQHandler                      
00004a27  DMA_IRQHandler                       
00004a27  Default_Handler                      
00004a27  GROUP0_IRQHandler                    
00004a27  HardFault_Handler                    
00004a27  I2C0_IRQHandler                      
00004a27  I2C1_IRQHandler                      
00004a27  NMI_Handler                          
00004a27  PendSV_Handler                       
00004a27  RTC_IRQHandler                       
00004a27  SPI0_IRQHandler                      
00004a27  SPI1_IRQHandler                      
00004a27  SVC_Handler                          
00004a27  TIMA0_IRQHandler                     
00004a27  TIMA1_IRQHandler                     
00004a27  TIMG0_IRQHandler                     
00004a27  TIMG12_IRQHandler                    
00004a27  TIMG6_IRQHandler                     
00004a27  TIMG7_IRQHandler                     
00004a27  TIMG8_IRQHandler                     
00004a27  UART0_IRQHandler                     
00004a27  UART1_IRQHandler                     
00004a27  UART2_IRQHandler                     
00004a27  UART3_IRQHandler                     
00004a2a  C$$EXIT                              
00004a2b  HOSTexit                             
00004a2f  Reset_Handler                        
00004a45  _system_pre_init                     
00004a50  asc2_1608                            
00005040  asc2_0806                            
00005260  __aeabi_ctype_table_                 
00005260  __aeabi_ctype_table_C                
00005480  __TI_Handler_Table_Base              
0000548c  __TI_Handler_Table_Limit             
00005494  __TI_CINIT_Base                      
000054a4  __TI_CINIT_Limit                     
000054a4  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202000f0  GraySensor                           
202001a0  Gray_Anolog                          
202001b0  ExISR_Flag                           
202001b4  Gray_Digtal                          
202001b8  Motor_Left                           
202001d8  Motor_Right                          
202001f8  Data_MotorEncoder                    
202001fc  __aeabi_errno                        
20200200  delayTick                            
20200208  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[231 symbols]
